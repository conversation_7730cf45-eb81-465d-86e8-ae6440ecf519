import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractInitialize } from '@test/AccessCtrl/helpers/contractInitialize'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { toBytes32 } from '@test/common/utils'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addRoleByIssuer()', () => {
  const issuerId = toBytes32('x11')
  const role = '0xffeeddccbbaa99887766554433221100ffeeddccbbaa99887766554433221100'

  describe('正常系', () => {
    let sender
    let accessCtrl: AccessCtrlInstance
    let accounts: SignerWithAddress[]

    before(async () => {
      ;({ accounts } = await contractFixture<AccessCtrlContractType>())
      sender = await accounts[7]
    })

    describe('Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態', () => {
      before(async () => {
        ;({ accessCtrl } = await contractInitialize({
          accounts,
          customAddress: { issuer: await sender.getAddress() },
        }))
      })

      it('権限が付与されること', async () => {
        const tx = await accessCtrlFuncs.addRoleByIssuer({
          accessCtrl,
          accounts,
          issuerId,
          role,
          account: await accounts[5].getAddress(),
          options: { sender },
        })

        const expectParams = {
          role,
          account: await accounts[5].getAddress(),
          sender: await sender.getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleGranted')
          .withArgs(...Object.values(expectParams))
        assert.ok(
          await accessCtrlFuncs.hasRole({
            accessCtrl,
            role,
            account: await accounts[5].getAddress(),
          }),
        )
      })
    })

    describe('accounts[5]に権限が付与されている状態', () => {
      it('同一アカウントに同一権限を付与した場合、RoleGrantedイベントが発火されないこと', async () => {
        const tx = await accessCtrlFuncs.addRoleByIssuer({
          accessCtrl,
          accounts,
          issuerId,
          role,
          account: await accounts[5].getAddress(),
          options: { sender },
        })

        await expect(tx).to.not.emit(accessCtrl, 'RoleGranted')
      })
    })
  })

  describe('準正常系', () => {
    let sender
    let accessCtrl: AccessCtrlInstance
    let accounts: SignerWithAddress[]

    before(async () => {
      ;({ accounts } = await contractFixture<AccessCtrlContractType>())
      sender = await accounts[7]
    })

    describe('Issuerのコントラクトアドレスにaccounts[7]のアドレスが設定されている状態', () => {
      before(async () => {
        ;({ accessCtrl } = await contractInitialize({
          accounts,
          customAddress: { issuer: await sender.getAddress() },
        }))
      })

      it('権限にAdmin権限を指定した場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addRoleByIssuer({
          accessCtrl,
          accounts,
          issuerId,
          role: BASE.ROLE.ADMIN,
          account: await accounts[5].getAddress(),
          options: { sender },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })

      it('呼び出し元がIssuerではない場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addRoleByIssuer({
          accessCtrl,
          accounts,
          issuerId,
          role,
          account: await accounts[5].getAddress(),
        })
        await expect(result).to.be.revertedWith(ERR.ISSUER.NOT_ISSUER_CONTRACT)
      })

      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addRoleByIssuer({
          accessCtrl,
          accounts,
          issuerId,
          role: BASE.ROLE.ADMIN,
          account: await accounts[5].getAddress(),
          options: { sender },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_ROLE)
      })
    })
  })
})
