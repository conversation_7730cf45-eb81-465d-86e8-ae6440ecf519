import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ROLLED_EOA_KEY } from '@test/AccessCtrl/helpers/constant'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { assertEqualForEachField, getExceededDeadline } from '@test/common/utils'
import { expect } from 'chai'
import { before } from 'mocha'

describe('checkAdminRole()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
        await accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[3].getAddress(),
        })
      })

      it('Admin権限がある場合、trueが返されること', async () => {
        const result = await accessCtrlFuncs.checkAdminRole({
          accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN },
        })

        assertEqualForEachField(result, { has: true, err: '' })
      })

      it('Admin権限でない署名の場合、falseが返されること', async () => {
        const result = await accessCtrlFuncs.checkAdminRole({
          accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.NOT_ADMIN },
        })

        assertEqualForEachField(result, { has: false, err: '' })
      })

      it('署名期限切れの場合、falseが返されること', async () => {
        const exceededDeadline = await getExceededDeadline()
        const result = await accessCtrlFuncs.checkAdminRole({
          accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, deadline: exceededDeadline },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_SIG_TIMEOUT })
      })

      it('Admin権限でない署名の場合、falseが返されること', async () => {
        const result = await accessCtrlFuncs.checkAdminRole({
          accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.NOT_ADMIN },
        })

        assertEqualForEachField(result, { has: false, err: '' })
      })

      it('署名が無効の場合、falseが返されること', async () => {
        const result = await accessCtrlFuncs.checkAdminRole({
          accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, sig: ['0x123456', ''] },
        })

        assertEqualForEachField(result, { has: false, err: ERR.ACTRL.ACTRL_BAD_SIG })
      })
    })
  })

  describe('準正常系', () => {
    describe('アカウントにAdmin権限が付与されている状態', () => {
      before(async () => {
        await accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[ROLLED_EOA_KEY.ADMIN].getAddress(),
        })
        await accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[3].getAddress(),
        })
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'
        // ganacheはcallでもrevertする
        const result = accessCtrlFuncs.checkAdminRole({
          accessCtrl,
          account: await accounts[3].getAddress(),
          options: { eoaKey: ROLLED_EOA_KEY.ADMIN, sig: [bad_sig, ''] },
        })
        await expect(result).to.be.reverted // OpenZeppelin revertのため文字列がDCF仕様と異なる
      })
    })
  })
})
