import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { ROLLED_EOA_KEY } from '@test/AccessCtrl/helpers/constant'
import { accessCtrlFuncs } from '@test/AccessCtrl/helpers/function'
import { AccessCtrlContractType } from '@test/AccessCtrl/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccessCtrlInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addAdminRole()', () => {
  let accessCtrl: AccessCtrlInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('初期状態', () => {
      it('Admin権限が追加できること', async () => {
        const tx = await accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[1].getAddress(),
        })

        const expectParams = {
          role: BASE.ROLE.ADMIN,
          account: await accounts[1].getAddress(),
          sender: await accounts[9].getAddress(),
        }
        await expect(tx)
          .to.emit(accessCtrl, 'RoleGranted')
          .withArgs(...Object.values(expectParams))
      })
    })

    describe('accounts[1]にAdmin権限が付与されている状態', () => {
      it('RoleGrantedイベントが発火されないこと', async () => {
        const tx = await accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[1].getAddress(),
        })

        await expect(tx).to.not.emit(accessCtrl, 'RoleGranted')
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, accessCtrl } = await contractFixture<AccessCtrlContractType>())
    })

    describe('初期状態', () => {
      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[1].getAddress(),
          options: {
            eoaKey: ROLLED_EOA_KEY.NOT_ADMIN,
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_NOT_ADMIN_ROLE)
      })

      it('署名が無効の場合、エラーがスローされること', async () => {
        const result = accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[1].getAddress(),
          options: {
            sig: ['0x12345678', ''],
          },
        })
        await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名が無効（not signature）の場合、エラーがスローされること', async () => {
        const bad_sig =
          '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000'
        const result = accessCtrlFuncs.addAdminRole({
          accessCtrl,
          from: accounts[9],
          account: await accounts[1].getAddress(),
          options: {
            sig: [bad_sig, ''],
          },
        })
        await expect(result).to.be.reverted // OpenZeppelin revertのため文字列がDCF仕様と異なる
      })
    })
  })
})
