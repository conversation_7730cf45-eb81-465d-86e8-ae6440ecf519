import '@nomicfoundation/hardhat-chai-matchers'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccessCtrlMockInstance,
  AccountSyncBridgeInstance,
  BusinessZoneAccountMockInstance,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  ProviderMockInstance,
  ValidatorMockInstance,
} from '@test/common/types'
import { expect } from 'chai'
import hre from 'hardhat'

describe('initialize()', () => {
  let accountSyncBridge: AccountSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let providerMock: ProviderMockInstance
  let validatorMock: ValidatorMockInstance
  let accessCtrlMock: AccessCtrlMockInstance
  let businessZoneAccountMock: BusinessZoneAccountMockInstance
  let ibcTokenMock: IBCTokenMockInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ ibcHand<PERSON>, providerMock, validatorMock, accessCtrlMock, businessZoneAccountMock, ibcTokenMock } =
        await contractFixture<AccountSyncBridgeContractType>())
    })

    beforeEach(async () => {
      accountSyncBridge = await (await hre.ethers.getContractFactory('AccountSyncBridge')).deploy()
    })

    describe('初期状態', () => {
      it('IBCHandlerのコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = accountSyncBridge.initialize(
          BASE.EMPTY_ADDRESS,
          await providerMock.getAddress(),
          await validatorMock.getAddress(),
          await accessCtrlMock.getAddress(),
          await businessZoneAccountMock.getAddress(),
          await ibcTokenMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('Providerコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          BASE.EMPTY_ADDRESS,
          await validatorMock.getAddress(),
          await accessCtrlMock.getAddress(),
          await businessZoneAccountMock.getAddress(),
          await ibcTokenMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('Validatorコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await providerMock.getAddress(),
          BASE.EMPTY_ADDRESS,
          await accessCtrlMock.getAddress(),
          await businessZoneAccountMock.getAddress(),
          await ibcTokenMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('AccessCtrlコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await providerMock.getAddress(),
          await validatorMock.getAddress(),
          BASE.EMPTY_ADDRESS,
          await businessZoneAccountMock.getAddress(),
          await ibcTokenMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('BusinessZoneAccountコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await providerMock.getAddress(),
          await validatorMock.getAddress(),
          await accessCtrlMock.getAddress(),
          BASE.EMPTY_ADDRESS,
          await ibcTokenMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('IBCTokenコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await providerMock.getAddress(),
          await validatorMock.getAddress(),
          await accessCtrlMock.getAddress(),
          await businessZoneAccountMock.getAddress(),
          BASE.EMPTY_ADDRESS,
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('should revert when initialized', async () => {
        await accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await providerMock.getAddress(),
          await validatorMock.getAddress(),
          await accessCtrlMock.getAddress(),
          await businessZoneAccountMock.getAddress(),
          await ibcTokenMock.getAddress(),
        )
        const result = accountSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await providerMock.getAddress(),
          await validatorMock.getAddress(),
          await accessCtrlMock.getAddress(),
          await businessZoneAccountMock.getAddress(),
          await ibcTokenMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
      })
    })
  })
})
