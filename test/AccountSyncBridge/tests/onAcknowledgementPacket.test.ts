import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { accountSyncBridgeFuncs } from '@test/AccountSyncBridge/helpers/function'
import { AccountSyncBridgeContractType } from '@test/AccountSyncBridge/helpers/types'
import { genPacket } from '@test/AccountSyncBridge/helpers/utils'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountSyncBridgeInstance, IBCHandlerInstance, ValidatorMockInstance } from '@test/common/types'
import { assert, expect } from 'chai'
import Web3 from 'web3'

declare let web3: Web3

describe('onAcknowledgementPacket', () => {
  let accountSyncBridge: AccountSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let validatorMock: ValidatorMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accountSyncBridge, ibcHand<PERSON>, validatorMock } = await contractFixture<AccountSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      beforeEach(async () => {
        await accountSyncBridgeFuncs.syncAccount({ accountSyncBridge })
      })

      it('FinZoneからpacketを受け取ることができた場合、BizZone管理のFinZoneアカウントがリセットされないこと', async () => {
        await accountSyncBridgeFuncs.acknowledgementPacket({
          ibcHandler,
          accountSyncBridge,
        })

        const accountData = await validatorMock.getAccount(BASE.BRIDGE.VALIDATOR_ID, BASE.BRIDGE.ACCOUNT_A)
        assert.equal(accountData[0].accountStatus, BASE.BRIDGE.ACCOUNT_A_STATUS, 'accountStatus')
      })
      it('should revert when caller is not ibc', async () => {
        const packetData = web3.eth.abi.encodeParameters([], [])
        const packet = genPacket(
          packetData,
          BASE.ZONE.BIZ,
          0,
          privateKey.sig(privateKey.key[BASE.EOA.ADMIN], ['bytes32', 'uint256'], [BASE.BRIDGE.VALIDATOR_ID, 0]),
        )
        const result = accountSyncBridge.onAcknowledgementPacket(packet, BASE.EMPTY_ADDRESS, BASE.EMPTY_ADDRESS)
        await expect(result).to.be.revertedWith(ERR.COMMON.CALLER_NOT_IBC)
      })
    })
  })
})
