import '@nomicfoundation/hardhat-chai-matchers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { ContractManagerInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { assert } from 'chai'

describe('version()', () => {
  let contractManager: ContractManagerInstance

  describe('version()', () => {
    describe('正常系', () => {
      before(async () => {
        ;({ contractManager } = await contractFixture<ContractManagerContractType>())
      })

      it('versionが取得できること', async () => {
        assert.strictEqual(await contractManagerFuncs.version({ contractManager }), BASE.APP.VERSION, 'version')
      })
    })
  })
})
