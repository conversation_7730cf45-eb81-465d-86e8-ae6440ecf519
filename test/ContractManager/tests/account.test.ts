import '@nomicfoundation/hardhat-chai-matchers'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, ContractManagerInstance } from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('account()', () => {
  let contractManager: ContractManagerInstance
  let account: AccountInstance
  describe('正常系', () => {
    before(async () => {
      ;({ account, contractManager } = await contractFixture<ContractManagerContractType>())
    })

    it('accountインスタンスのアドレスが取得できること', async () => {
      const result = await contractManagerFuncs.account({ contractManager })

      assert.equal(result, await account.getAddress())
    })
  })
})
