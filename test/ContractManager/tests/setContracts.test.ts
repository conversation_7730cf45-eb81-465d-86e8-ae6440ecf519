import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import { getExceededDeadline } from '@test/common/utils'
import { contractsInitialize } from '@test/ContractManager/helpers/contractsInitialize'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ContractManagerContractType } from '@test/ContractManager/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import Web3 from 'web3'

declare let web3: Web3

// Chai global vars
declare let assert: Chai.Assert

describe('setContracts()', () => {
  let contractManager: ContractManagerInstance
  let accessCtrl: AccessCtrlInstance
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let token: TokenInstance
  let ibcToken: IBCTokenInstance
  let financialCheck: FinancialCheckInstance
  let transferProxy: TransferProxyInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({
        accounts,
        contractManager,
        accessCtrl,
        provider,
        issuer,
        validator,
        account,
        financialZoneAccount,
        businessZoneAccount,
        token,
        financialCheck,
        transferProxy,
        ibcToken,
      } = await helpers.loadFixture(contractsInitialize))
    })

    it('任意の値でそれぞれのコントラクトアドレスが設定されること', async () => {
      const providerAddress = web3.utils.padLeft('0x01', 40)
      const issuerAddress = web3.utils.padLeft('0x02', 40)
      const validatorAddress = web3.utils.padLeft('0x03', 40)
      const accountAddress = web3.utils.padLeft('0x04', 40)
      const financialZoneAccountAddress = web3.utils.padLeft('0x05', 40)
      const businessZoneAccountAddress = web3.utils.padLeft('0x06', 40)
      const tokenAddress = web3.utils.padLeft('0x05', 40)
      const financialCheckAddress = web3.utils.padLeft('0x06', 40)
      const transferProxyAddress = web3.utils.padLeft('0x06', 40)
      const ibcTokenAddress = web3.utils.padLeft('0x05', 40)

      await contractManagerFuncs.setContracts({
        contractManager,
        accounts,
        addresses: [
          await accessCtrl.getAddress(), // setContracts内部でAccessCtrl#checkAdminRoleを呼び出しているので、AccessCtrlのアドレスは任意の値が設定できない
          providerAddress,
          issuerAddress,
          validatorAddress,
          accountAddress,
          financialZoneAccountAddress,
          businessZoneAccountAddress,
          tokenAddress,
          ibcTokenAddress,
          financialCheckAddress,
          transferProxyAddress,
        ],
      })

      assert.equal(await contractManagerFuncs.accessCtrl({ contractManager }), await accessCtrl.getAddress())
      assert.equal(await contractManager['provider()'](), providerAddress) // DCPF-25734 Fix function overloading (https://github.com/ethers-io/ethers.js/issues/407)
      assert.equal(await contractManagerFuncs.issuer({ contractManager }), issuerAddress)
      assert.equal(await contractManagerFuncs.validator({ contractManager }), validatorAddress)
      assert.equal(await contractManagerFuncs.account({ contractManager }), accountAddress)
      assert.equal(await contractManagerFuncs.financialZoneAccount({ contractManager }), financialZoneAccountAddress)
      assert.equal(await contractManagerFuncs.businessZoneAccount({ contractManager }), businessZoneAccountAddress)
      assert.equal(await contractManagerFuncs.token({ contractManager }), tokenAddress)
      assert.equal(await contractManagerFuncs.financialCheck({ contractManager }), financialCheckAddress)
      assert.equal(await contractManagerFuncs.transferProxy({ contractManager }), transferProxyAddress)
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({
        accounts,
        contractManager,
        accessCtrl,
        provider,
        issuer,
        validator,
        account,
        financialZoneAccount,
        businessZoneAccount,
        token,
        financialCheck,
        transferProxy,
        ibcToken,
      } = await contractFixture<ContractManagerContractType>())
    })

    it('引数に指定するAccessCtrlのアドレスがAccessCtrlのコントラクトアドレスでない場合、エラーがスローされること', async () => {
      const result = contractManagerFuncs.setContracts({
        contractManager,
        accounts,
        addresses: [
          await accounts[1].getAddress(),
          await provider.getAddress(),
          await issuer.getAddress(),
          await validator.getAddress(),
          await account.getAddress(),
          await financialZoneAccount.getAddress(),
          await businessZoneAccount.getAddress(),
          await token.getAddress(),
          await ibcToken.getAddress(),
          await financialCheck.getAddress(),
          await transferProxy.getAddress(),
        ],
      })
      await expect(result).to.be.revertedWithoutReason()
      // インスタンスアドレスが異なりメソッド呼び出しができないエラーのため、reverts時のエラーメッセージはない
    })

    it('Admin権限ではない署名の場合、エラーがスローされること', async () => {
      const result = contractManagerFuncs.setContracts({
        contractManager,
        accounts,
        addresses: [
          await accessCtrl.getAddress(),
          await provider.getAddress(),
          await issuer.getAddress(),
          await validator.getAddress(),
          await account.getAddress(),
          await financialZoneAccount.getAddress(),
          await businessZoneAccount.getAddress(),
          await token.getAddress(),
          await ibcToken.getAddress(),
          await financialCheck.getAddress(),
          await transferProxy.getAddress(),
        ],
        options: { eoaKey: BASE.EOA.PROV1 },
      })
      await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_NOT_ADMIN_ROLE)
    })

    it('署名期限切れの場合、エラーがスローされること', async () => {
      const exceededDeadline = await getExceededDeadline()

      const result = contractManagerFuncs.setContracts({
        contractManager,
        accounts,
        addresses: [
          await accessCtrl.getAddress(),
          await provider.getAddress(),
          await issuer.getAddress(),
          await validator.getAddress(),
          await account.getAddress(),
          await financialZoneAccount.getAddress(),
          await businessZoneAccount.getAddress(),
          await token.getAddress(),
          await ibcToken.getAddress(),
          await financialCheck.getAddress(),
          await transferProxy.getAddress(),
        ],
        options: { deadline: exceededDeadline },
      })
      await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
    })

    it('署名無効の場合、エラーがスローされること', async () => {
      const result = contractManagerFuncs.setContracts({
        contractManager,
        accounts,
        addresses: [
          await accessCtrl.getAddress(),
          await provider.getAddress(),
          await issuer.getAddress(),
          await validator.getAddress(),
          await account.getAddress(),
          await financialZoneAccount.getAddress(),
          await businessZoneAccount.getAddress(),
          await token.getAddress(),
          await ibcToken.getAddress(),
          await financialCheck.getAddress(),
          await transferProxy.getAddress(),
        ],
        options: { sig: ['0x1234', ''] },
      })
      await expect(result).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
    })
  })
})
