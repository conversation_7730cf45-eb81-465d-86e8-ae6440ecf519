import '@nomicfoundation/hardhat-chai-matchers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BusinessZoneAccountInstance, ContractManagerInstance } from '@test/common/types'
import { expect } from 'chai'

describe('initialize()', () => {
  let businessZoneAccount: BusinessZoneAccountInstance
  let contractManager: ContractManagerInstance

  describe('正常系', () => {
    before(async () => {
      ;({ businessZoneAccount, contractManager } = await contractFixture<BusinessZoneAccountContractType>())
    })

    it('should revert when initialized', async () => {
      const result = businessZoneAccount.initialize(await contractManager.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
