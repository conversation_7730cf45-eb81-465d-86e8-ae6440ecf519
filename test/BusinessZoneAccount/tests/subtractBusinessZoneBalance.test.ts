import { BASE, ERR } from '@test/common/consts'
import { BusinessZoneAccountInstance, TokenInstance } from '@test/common/types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { contractFixture } from '@test/common/contractFixture'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('subtractBusinessZoneBalance()', () => {
  let businessZoneAccount: BusinessZoneAccountInstance
  let token: TokenInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, businessZoneAccount, token } = await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        const result = businessZoneAccount
          .connect(accounts[0])
          .subtractBusinessZoneBalance(BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT1.ID, 100)
        await expect(result).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })

    describe('Fake call from token contract', () => {
      it('should pass require token contract but revert with not valid zoneId', async () => {
        // Hack: Fake token can call subtractBusinessZoneBalance
        // UNREACHABLE: Call move from token to tokenService so token case never reach
        // #TODO: This for coverage only, can remove when the requirement changed
        await helpers.impersonateAccount(await token.getAddress())
        await helpers.setBalance(await token.getAddress(), 100n ** 18n)
        const fakeToken = await ethers.getSigner(await token.getAddress())
        const result = businessZoneAccount
          .connect(fakeToken)
          .subtractBusinessZoneBalance(BASE.ZONE_ID.EMPTY_ID, BASE.ACCOUNT.ACCOUNT1.ID, 100)
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_INVALID_VAL)
        await helpers.stopImpersonatingAccount(await token.getAddress())
      })
    })
  })
})
