import '@nomicfoundation/hardhat-chai-matchers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BusinessZoneAccountInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'

describe('hasAccountByZone()', () => {
  let businessZoneAccount: BusinessZoneAccountInstance

  describe('正常系', () => {
    before(async () => {
      ;({ businessZoneAccount } = await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('zoneIdが不正である場合、エラーがスローされること', async () => {
        const result = await businessZoneAccount.hasAccountByZone(BASE.ZONE_ID.EMPTY_ID, BASE.ACCOUNT.ACCOUNT1.ID)
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })

      it('accountIdが不正である場合、エラーがスローされること', async () => {
        const result = await businessZoneAccount.hasAccountByZone(BASE.ZONE_ID.ID0, BASE.ACCOUNT.EMPTY.ID)
        const expected = {
          success: false,
          err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL,
        }
        assertEqualForEachField(result, expected)
      })
    })
  })
})
