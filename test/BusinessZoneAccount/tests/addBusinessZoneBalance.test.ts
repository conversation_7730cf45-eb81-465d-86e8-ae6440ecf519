import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BusinessZoneAccountInstance } from '@test/common/types'
import { expect } from 'chai'

describe('addBusinessZoneBalance()', () => {
  let accounts: SignerWithAddress[]
  let businessZoneAccount: BusinessZoneAccountInstance
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, businessZoneAccount } = await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        const result = businessZoneAccount
          .connect(accounts[0])
          .addBusinessZoneBalance(BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT1.ID, 100)
        await expect(result).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })
  })
})
