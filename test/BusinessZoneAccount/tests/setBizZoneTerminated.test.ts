import '@nomicfoundation/hardhat-chai-matchers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BusinessZoneAccountInstance } from '@test/common/types'
import { expect } from 'chai'

describe('setBizZoneTerminated()', () => {
  let businessZoneAccount: BusinessZoneAccountInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ businessZoneAccount } = await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('初期状態', () => {
      it('should revert when caller is not validator contract', async () => {
        const result = businessZoneAccount.setBizZoneTerminated(BASE.ZONE_ID.ID0, BASE.ACCOUNT.ACCOUNT1.ID)
        await expect(result).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })
  })
})
