import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

describe('isFrozen()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<AccountContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await providerFuncs.addToken({ provider, accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator, accounts })
      })

      it('should return error when account is not registered', async () => {
        const result = await account.isFrozen(BASE.ACCOUNT.ACCOUNT5.ID)
        assertEqualForEachField(result, { frozen: false, err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })
    })
  })
})
