import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { ERR } from '@test/common/consts'
import { AccountInstance, ContractManagerInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('initialize()', () => {
  let account: AccountInstance
  let contractManager: ContractManagerInstance
  describe('正常系', () => {
    before(async () => {
      ;({ account, contractManager } = await contractFixture<AccountContractType>())
    })

    it('should revert when initialized', async () => {
      const result = account.initialize(await contractManager.getAddress())
      await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
    })
  })
})
