import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { ethers } from 'hardhat'
import { before } from 'mocha'

describe('addAccount()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]
  // addAccount のテストは validator.addAccount から呼ばれるため validator のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
        const result = account
          .connect(accounts[1])
          .addAccount(BASE.ACCOUNT.ACCOUNT0.ID, BASE.ACCOUNT.ACCOUNT0.NAME, BASE.VALID.VALID0.ID)
        await expect(result).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider,
          accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider,
          accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer, accounts })
        await validatorFuncs.addValidator({ validator, accounts })
        await validatorFuncs.addAccount({
          validator,
          accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          },
        })
      })

      it('should revert when accountId is already registered and called from fake validator', async () => {
        // Hack: Fake validator can call addAccount
        // UNREACHABLE false case: This test only for coverage, not for real case
        await helpers.impersonateAccount(await validator.getAddress())
        await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
        const fakeValidator = await ethers.getSigner(await validator.getAddress())
        const result = account
          .connect(fakeValidator)
          .addAccount(BASE.ACCOUNT.ACCOUNT1.ID, BASE.ACCOUNT.ACCOUNT1.NAME, BASE.VALID.VALID0.ID) // Fake validator call
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_EXIST)
      })

      it('should revert when accountId is 0x00 is  and called from fake validator', async () => {
        // Hack: Fake validator can call addAccount
        // UNREACHABLE false case: This test only for coverage, not for real case
        await helpers.impersonateAccount(await validator.getAddress())
        await helpers.setBalance(await validator.getAddress(), 100n ** 18n)
        const fakeValidator = await ethers.getSigner(await validator.getAddress())
        const result = account
          .connect(fakeValidator)
          .addAccount(BASE.ACCOUNT.EMPTY.ID, BASE.ACCOUNT.EMPTY.NAME, BASE.VALID.VALID0.ID) // Fake validator call
        await expect(result).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST)
      })
    })
  })
})
