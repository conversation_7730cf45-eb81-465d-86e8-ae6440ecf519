import { AccountContractType } from '@/test/Account/helpers/types'
import { contractFixture } from '@/test/common/contractFixture'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addAccountRole()', () => {
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  // addAccountRole のテストは issuer.addAccountRole から呼ばれるため issuer のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がIssuerではない場合、エラーがスローされること', async () => {
        const result = account
          .connect(accounts[1])
          .addAccountRole(BASE.ACCOUNT.ACCOUNT0.ID, await accounts[BASE.EOA.ACCOUNT].getAddress(), BASE.TRACE_ID)
        await expect(result).to.be.revertedWith(ERR.ISSUER.NOT_ISSUER_CONTRACT)
      })
    })
  })
})
