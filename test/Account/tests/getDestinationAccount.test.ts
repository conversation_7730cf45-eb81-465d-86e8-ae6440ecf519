import '@nomicfoundation/hardhat-chai-matchers'
import { accountFuncs } from '@test/Account/helpers/function'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'

describe('getDestinationAccount()', () => {
  let account: AccountInstance

  // getDestinationAccount のテストは validator.getDestinationAccount から呼ばれるため validator のテストで実施する, ここでは呼び出し元検証のみ行う
  describe('準正常系', () => {
    before(async () => {
      ;({ account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
        await expect(
          accountFuncs.getDestinationAccount({ account, params: [BASE.ACCOUNT.ACCOUNT0.ID] }),
        ).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })
  })
})
