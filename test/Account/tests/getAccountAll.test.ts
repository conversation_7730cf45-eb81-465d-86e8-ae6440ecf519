import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'

describe('getAccountAll()', () => {
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がValidatorではない場合、エラーがスローされること', async () => {
        const result = account.connect(accounts[1]).getAccountAll(BASE.ACCOUNT.ACCOUNT0.ID)
        await expect(result).to.be.revertedWith(ERR.VALID.NOT_VALIDATOR_CONTRACT)
      })
    })
  })
})
