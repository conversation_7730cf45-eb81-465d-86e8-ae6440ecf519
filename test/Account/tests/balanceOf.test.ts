import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import * as helpers from '@nomicfoundation/hardhat-network-helpers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IBCTokenInstance } from '@test/common/types'
import { assertEqualForEachField } from '@test/common/utils'
import { ethers } from 'hardhat'

describe('balanceOf()', () => {
  let account: AccountInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーが返されること', async () => {
        const result = await account.connect(accounts[1]).balanceOf(BASE.ACCOUNT.ACCOUNT0.ID)
        assertEqualForEachField(result, { balance: 0n, err: ERR.TOKEN.INVALID_CALLER_ADDRESS })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, account, ibcToken } = await contractFixture<AccountContractType>())
    })

    describe('account is not registered', () => {
      let fakeIbcToken
      before(async () => {
        await helpers.impersonateAccount(await ibcToken.getAddress())
        await helpers.setBalance(await ibcToken.getAddress(), 100n ** 18n)
        fakeIbcToken = await ethers.getSigner(await ibcToken.getAddress())
      })

      it('should revert when accountId is not registered', async () => {
        // Hack: Fake token can call balanceOf
        // UNREACHABLE false case: This test only for coverage, not for real case
        const result = await account.connect(fakeIbcToken).balanceOf(BASE.ACCOUNT.ACCOUNT1.ID)
        assertEqualForEachField(result, { err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST })
      })

      it('should revert when accountId is invalid and called from fake token', async () => {
        // Hack: Fake token can call balanceOf
        // UNREACHABLE false case: This test only for coverage, not for real case
        const result = await account.connect(fakeIbcToken).balanceOf(BASE.ACCOUNT.EMPTY.ID)
        assertEqualForEachField(result, { err: ERR.ACCOUNT.ACCOUNT_INVALID_VAL })
      })
    })
  })
})
