import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance } from '@test/common/types'
import { expect } from 'chai'

describe('mint()', () => {
  // mint のテストは token.mint から呼ばれるため token のテストで実施する, ここでは呼び出し元検証のみ行う

  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, account } = await contractFixture<AccountContractType>())
    })

    describe('初期状態', () => {
      it('呼び出し元がTokenではない場合、エラーがスローされること', async () => {
        const result = account.connect(accounts[1]).mint(BASE.ACCOUNT.ACCOUNT0.ID, 1000) // tokenコントラクト以外をfromに設定する
        await expect(result).to.be.revertedWith(ERR.TOKEN.NOT_TOKEN_CONTRACT)
      })
    })
  })
})
