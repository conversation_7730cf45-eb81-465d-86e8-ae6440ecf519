import '@nomicfoundation/hardhat-chai-matchers'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccessCtrlMockInstance,
  AccountMockInstance,
  BalanceSyncBridgeInstance,
  IBCHandlerInstance,
  IBCTokenMockInstance,
} from '@test/common/types'
import { expect } from 'chai'
import { ethers } from 'hardhat'

const BalanceSyncBridgeFactory = ethers.getContractFactory('BalanceSyncBridge')

describe('initialize()', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcHandler: IBCHandlerInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accountMock: AccountMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ ibc<PERSON><PERSON><PERSON>, ibcTokenMock, accountMock, accessCtrlMock } =
        await contractFixture<BalanceSyncBridgeContractType>())
    })

    beforeEach(async () => {
      balanceSyncBridge = await (await BalanceSyncBridgeFactory).deploy()
    })

    describe('初期状態', () => {
      it('IBCHandlerのコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = balanceSyncBridge.initialize(
          BASE.EMPTY_ADDRESS,
          await ibcTokenMock.getAddress(),
          await accountMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('IBCTokenコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = balanceSyncBridge.initialize(
          await ibcHandler.getAddress(),
          BASE.EMPTY_ADDRESS,
          await accountMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('Accountコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = balanceSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          BASE.EMPTY_ADDRESS,
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('AccessCtrlコントラクトアドレスが空の場合、エラーをスローすること', async () => {
        const result = balanceSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          await accountMock.getAddress(),
          BASE.EMPTY_ADDRESS,
        )
        await expect(result).to.be.revertedWith(ERR.IBC.IBC_INVALID_VAL)
      })
      it('should revert if the contract is already initialized', async () => {
        await balanceSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          await accountMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        const result = balanceSyncBridge.initialize(
          await ibcHandler.getAddress(),
          await ibcTokenMock.getAddress(),
          await accountMock.getAddress(),
          await accessCtrlMock.getAddress(),
        )
        await expect(result).to.be.revertedWith(ERR.INITIALIZER.ALREADY_INIT)
      })
    })
  })
})
