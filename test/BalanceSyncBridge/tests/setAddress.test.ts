import privateKey from '@/privateKey'
import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { balanceSyncBridgeFuncs } from '@test/BalanceSyncBridge/helpers/function'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  AccessCtrlMockInstance,
  AccountMockInstance,
  BalanceSyncBridgeInstance,
  IBCTokenMockInstance,
} from '@test/common/types'
import { expect } from 'chai'

describe('setAddress()', () => {
  let accounts: SignerWithAddress[]
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance
  let accountMock: AccountMockInstance
  let accessCtrlMock: AccessCtrlMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, balanceSyncBridge, ibcTokenMock, accountMock, accessCtrlMock } =
        await contractFixture<BalanceSyncBridgeContractType>())
    })

    describe('初期状態', () => {
      it('_ibcToken, _account, _accessCtrl can be set normally', async () => {
        await accessCtrlMock.addAdminRole(accounts[0], 0, BASE.TRACE_ID)
        const tx = await balanceSyncBridgeFuncs.setAddress({
          balanceSyncBridge,
          ibcTokenMockAddress: await ibcTokenMock.getAddress(),
          accountMockAddress: await accountMock.getAddress(),
          accessCtrlMockAddress: await accessCtrlMock.getAddress(),
        })

        // TODO: setAddress don't have event and variable is private
        // Should change this to check the value of the variable when event is implemented
        await expect(tx).to.been.ok
      })
    })

    describe('準正常系', () => {
      before(async () => {
        ;({ accounts, balanceSyncBridge, ibcTokenMock, accountMock, accessCtrlMock } =
          await contractFixture<BalanceSyncBridgeContractType>())
      })

      it('should revert when not admin', async () => {
        const result = balanceSyncBridgeFuncs.setAddress({
          balanceSyncBridge,
          ibcTokenMockAddress: await ibcTokenMock.getAddress(),
          accountMockAddress: await accountMock.getAddress(),
          accessCtrlMockAddress: await accessCtrlMock.getAddress(),
          options: {
            privateKeyForSig: privateKey[1],
          },
        })
        await expect(result).to.be.revertedWith(ERR.COMMON.NOT_ADMIN_ROLE)
      })
    })
  })
})
