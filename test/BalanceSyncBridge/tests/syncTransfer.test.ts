import '@nomicfoundation/hardhat-chai-matchers'
import { balanceSyncBridgeFuncs } from '@test/BalanceSyncBridge/helpers/function'
import { BalanceSyncBridgeContractType } from '@test/BalanceSyncBridge/helpers/types'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { BalanceSyncBridgeInstance, IBCTokenMockInstance } from '@test/common/types'
import { expect } from 'chai'

describe('syncTransfer', () => {
  let balanceSyncBridge: BalanceSyncBridgeInstance
  let ibcTokenMock: IBCTokenMockInstance

  describe('正常系', () => {
    before(async () => {
      ;({ balanceSyncBridge, ibcTokenMock } = await contractFixture<BalanceSyncBridgeContractType>())
    })

    // TODO HS
    describe('初期状態', () => {
      beforeEach(async () => {
        await ibcTokenMock.updateBalance(BASE.BRIDGE.ACCOUNT_A, BASE.BRIDGE.AMOUNT, true)
      })

      it('BizZoneからFinZoneへの残高同期が成功した場合、BizZoneの残高がリセットされていないこと', async () => {
        const fromAccountBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_A)
        const toAccountBefore = await ibcTokenMock.balanceOf(BASE.ZONE.FIN, BASE.BRIDGE.ACCOUNT_B)

        await balanceSyncBridgeFuncs.syncTransfer({ balanceSyncBridge })

        const fromAccount = await ibcTokenMock.balanceOf(BASE.ZONE.BIZ, BASE.BRIDGE.ACCOUNT_A)
        const toAccount = await ibcTokenMock.balanceOf(BASE.ZONE.BIZ, BASE.BRIDGE.ACCOUNT_B)

        await expect(fromAccount[0]).to.be.equal(fromAccountBefore[0])
        await expect(toAccount[0]).to.be.equal(toAccountBefore[0])
      })
    })
  })
})
